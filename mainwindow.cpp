﻿#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "onevideo.h"
#include "camerascanwidget.h"
#include "configdialog.h"
#include "configmanager.h"
#include "logmanager.h"

#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QStatusBar>
#include <QScrollArea>
#include <QPushButton>
#include <QMessageBox>
#include <QTimer>
#include <QDateTime>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QButtonGroup>
#include <QToolButton>
#include <QStackedLayout>
#include <QMouseEvent>
#include <QApplication>
#include <QScreen>

#include <QResizeEvent>
#include <QDebug>

MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent), ui(new Ui::MainWindow), mousePressed(false)
{
    ui->setupUi(this);

    // 从配置文件中读取默认分屏模式
    int defaultMode = ConfigManager::instance().getDefaultDisplayMode();
    if (defaultMode == 1 || defaultMode == 4 || defaultMode == 9)
    {
        currentDisplayMode = static_cast<DisplayMode>(defaultMode);
    }
    else
    {
        currentDisplayMode = FourMode; // 如果配置文件中的值无效，使用四分屏作为默认值
    }

    resize(1200, 800);
    setMinimumSize(OneVideo::WIDTH + 320, OneVideo::HEIGHT + 50);

    // 设置无边框窗口
    setWindowFlags(Qt::FramelessWindowHint);

    // 设置窗口背景图片
    QPixmap backgroundPixmap(":/images/background.png");
    QPalette palette;
    palette.setBrush(QPalette::Window, backgroundPixmap);
    this->setPalette(palette);
    this->setAutoFillBackground(true);

    // 创建标题栏按钮
    QWidget *titleButtonsWidget = new QWidget(this);
    QHBoxLayout *titleButtonsLayout = new QHBoxLayout(titleButtonsWidget);
    titleButtonsLayout->setContentsMargins(0, 0, 5, 0);
    titleButtonsLayout->setSpacing(5);

    // 创建最小化按钮
    QPushButton *minButton = new QPushButton("─", titleButtonsWidget);
    minButton->setFixedSize(24, 24);
    minButton->setStyleSheet("QPushButton { color: white; background-color: transparent; border: none; font-weight: bold; } "
                             "QPushButton:hover { background-color: rgba(255, 255, 255, 30); } "
                             "QPushButton:pressed { background-color: rgba(255, 255, 255, 50); }");
    connect(minButton, &QPushButton::clicked, this, &MainWindow::showMinimized);

    // 创建最大化按钮
    QPushButton *maxButton = new QPushButton("□", titleButtonsWidget);
    maxButton->setFixedSize(24, 24);
    maxButton->setStyleSheet("QPushButton { color: white; background-color: transparent; border: none; font-weight: bold; } "
                             "QPushButton:hover { background-color: rgba(255, 255, 255, 30); } "
                             "QPushButton:pressed { background-color: rgba(255, 255, 255, 50); }");
    connect(maxButton, &QPushButton::clicked, this, &MainWindow::toggleMaximized);

    // 创建关闭按钮
    QPushButton *closeButton = new QPushButton("×", titleButtonsWidget);
    closeButton->setFixedSize(24, 24);
    closeButton->setStyleSheet("QPushButton { color: white; background-color: transparent; border: none; font-weight: bold; } "
                               "QPushButton:hover { background-color: rgba(255, 0, 0, 150); } "
                               "QPushButton:pressed { background-color: red; }");
    connect(closeButton, &QPushButton::clicked, this, &MainWindow::close);

    // 添加按钮到布局
    titleButtonsLayout->addWidget(minButton);
    titleButtonsLayout->addWidget(maxButton);
    titleButtonsLayout->addWidget(closeButton);

    // 设置标题文字
    ui->titleLabel->setText(ConfigManager::instance().getAppName());
    ui->titleLabel->setStyleSheet("QLabel { color: white; font-weight: bold; font-size: 22px; text-align: center; }");

    // 将标题栏按钮添加到banner布局
    QHBoxLayout *bannerLayout = qobject_cast<QHBoxLayout *>(ui->bannerWidget->layout());
    if (bannerLayout)
    {
        bannerLayout->addWidget(titleButtonsWidget);
    }

    // 设置banner样式
    ui->bannerWidget->setMouseTracking(true);
    ui->bannerWidget->installEventFilter(this);

    // 设置监控画面标题样式
    ui->monitorTitleLabel->setStyleSheet("QLabel { color: #FFFFFF; background-color: #003366; padding: 5px; font-weight: bold; font-size: 14px; }");

    // 隐藏监控画面标题和显示模式栏但保留代码
    ui->monitorTitleLabel->setVisible(false);
    ui->displayModeBar->setVisible(false);

    // 设置右侧操作面板为完全透明
    ui->rightWidget->setStyleSheet("QWidget { background-color: transparent; border: none; }");
    ui->rightWidget->setAttribute(Qt::WA_TranslucentBackground);
    ui->rightWidget->setAutoFillBackground(false);

    // 设置视频容器样式
    ui->centerWidget->setStyleSheet("QWidget { background-image: url(:/images/monitor_bg.png); background-position: center; background-repeat: no-repeat; background-size: cover; }");

    // 不再创建菜单栏
    // createMenu();
    createContent();

    // 设置分割器比例
    ui->mainSplitter->setStretchFactor(0, 1); // 左侧占比小
    ui->mainSplitter->setStretchFactor(1, 5); // 中间占比大
    ui->mainSplitter->setStretchFactor(2, 1); // 右侧占比小

    // 设置分割器初始位置
    QList<int> sizes;
    // sizes << 0 << 1500;
    sizes << 0 << 900 << 200; // 左侧0像素，中间900像素，右侧200像素
    ui->mainSplitter->setSizes(sizes);

    timer = new QTimer(this);
    connect(timer, SIGNAL(timeout()), SLOT(timerSlot()));
    timer->start(1000);

    // 保存窗口位置和大小，用于恢复
    normalGeometry = geometry();
}

MainWindow::~MainWindow()
{
    delete ui;
    timer->deleteLater();
    // 直接清理资源，而不是调用removeWindowSlot
    for (OneVideo *one : showList)
    {
        one->deleteLater();
    }
    showList.clear();
}

void MainWindow::createMenu()
{
    // 菜单栏
    QMenuBar *menuBar = new QMenuBar(this);

    QMenu *menuFile = new QMenu("文件(&F)", menuBar);
    // QMenu *menuSetting = new QMenu("设置(&S)", menuBar);
    QMenu *menuHelp = new QMenu("帮助(&H)", menuBar);
    menuBar->addAction(menuFile->menuAction());
    // menuBar->addAction(menuSetting->menuAction());
    menuBar->addAction(menuHelp->menuAction());
    setMenuBar(menuBar);

    // QAction *actionAdd = new QAction("添加窗口(&A)", menuFile);
    // QAction *actionRemove = new QAction("删除窗口(&D)", menuFile);
    QAction *actionExit = new QAction("退出(&Q)", menuFile);
    // QAction *actionConfig = new QAction("扫描配置(&C)", menuSetting);
    QAction *actionAbout = new QAction("关于(&A)", menuHelp);
    // menuFile->addAction(actionAdd);
    // menuFile->addAction(actionRemove);
    // menuFile->addSeparator();
    menuFile->addAction(actionExit);
    // menuSetting->addAction(actionConfig);
    menuHelp->addAction(actionAbout);

    // connect(actionAdd, SIGNAL(triggered(bool)), SLOT(addWindowSlot()));
    // connect(actionRemove, SIGNAL(triggered(bool)), SLOT(removeWindowSlot()));
    connect(actionExit, SIGNAL(triggered(bool)), SLOT(close()));
    // connect(actionConfig, SIGNAL(triggered(bool)), SLOT(showConfigDialog()));
    connect(actionAbout, SIGNAL(triggered(bool)), SLOT(aboutSlot()));
}

void MainWindow::createContent()
{
    // 创建左侧摄像头扫描界面
    cameraScanWidget = new CameraScanWidget(ui->leftWidget);
    cameraScanWidget->setMinimumWidth(350);

    // 隐藏左侧控件但保留代码
    ui->leftWidget->setVisible(false);
    // 或者使用样式表隐藏：ui->leftWidget->setStyleSheet("QWidget { width: 0px; }");

    // 设置mainContent为视频容器
    mainContent = ui->mainContent;

    // 创建显示模式切换按钮
    createDisplayModeButtons();

    // 添加按钮 - 隐藏添加按钮
    addBtn = new QPushButton("+", mainContent);
    addBtn->setGeometry(0, 0, OneVideo::WIDTH, OneVideo::HEIGHT);
    addBtn->setFont(QFont("黑体", 200, 87));
    addBtn->setVisible(false); // 设置为不可见

    // 连接信号槽 - 注释掉添加按钮的信号连接
    // connect(addBtn, SIGNAL(clicked(bool)), SLOT(addWindowSlot()));
    connect(cameraScanWidget, &CameraScanWidget::cameraSelected, this, &MainWindow::onCameraSelected);
    connect(cameraScanWidget, &CameraScanWidget::cameraDeselected, this, &MainWindow::onCameraDeselected);
    connect(cameraScanWidget, &CameraScanWidget::cameraNameUpdated, this, &MainWindow::onCameraNameUpdated);

    // 连接右侧操作按钮的信号
    connect(ui->eavesdropButton, &QPushButton::clicked, this, &MainWindow::onEavesdropClicked);
    connect(ui->listenButton, &QPushButton::clicked, this, &MainWindow::onListenClicked);
    connect(ui->screenshotButton, &QPushButton::clicked, this, &MainWindow::onScreenshotClicked);
    connect(ui->recordButton, &QPushButton::clicked, this, &MainWindow::onRecordClicked);
}

void MainWindow::createDisplayModeButtons()
{
    // 使用UI文件中的displayModeBar
    QHBoxLayout *modeLayout = qobject_cast<QHBoxLayout *>(ui->displayModeBar->layout());
    if (!modeLayout)
    {
        return;
    }

    // 清除现有的spacer
    QLayoutItem *item;
    while ((item = modeLayout->takeAt(0)) != nullptr)
    {
        delete item;
    }

    // 添加左侧弹簧使按钮组居中
    modeLayout->addStretch();

    // 创建按钮组
    displayModeGroup = new QButtonGroup(this);

    // 单画面按钮已注释
    // 创建垂直布局来包含按钮和标签
    QVBoxLayout *singleLayout = new QVBoxLayout();
    singleLayout->setContentsMargins(0, 0, 0, 0);
    singleLayout->setSpacing(2);
    QWidget *singleWidget = new QWidget();
    singleWidget->setLayout(singleLayout);

    // 单画面按钮
    singleModeBtn = new QToolButton();
    singleModeBtn->setIcon(QIcon(":/images/1_def.png"));
    singleModeBtn->setIconSize(QSize(25, 25));
    singleModeBtn->setToolTip("单画面");
    singleModeBtn->setCheckable(true);
    singleModeBtn->setChecked(currentDisplayMode == SingleMode); // 根据当前模式设置选中状态
    singleModeBtn->setAutoExclusive(true);
    singleModeBtn->setFixedSize(30, 30);
    displayModeGroup->addButton(singleModeBtn, SingleMode);

    // 添加单画面文字标签
    singleLabel = new QLabel("单画面");
    singleLabel->setAlignment(Qt::AlignCenter);
    singleLabel->setStyleSheet("QLabel { color: white; font-size: 12px; font-weight: bold; }");

    // 添加按钮和标签到垂直布局
    singleLayout->addWidget(singleModeBtn, 0, Qt::AlignCenter);
    singleLayout->addWidget(singleLabel, 0, Qt::AlignCenter);

    // 添加到主布局
    modeLayout->addWidget(singleWidget);

    // 四画面布局已注释
    QVBoxLayout *fourLayout = new QVBoxLayout();
    fourLayout->setContentsMargins(0, 0, 0, 0);
    fourLayout->setSpacing(2);
    QWidget *fourWidget = new QWidget();
    fourWidget->setLayout(fourLayout);

    // 4画面按钮
    fourModeBtn = new QToolButton();
    fourModeBtn->setIcon(QIcon(":/images/4_def.png"));
    fourModeBtn->setIconSize(QSize(25, 25));
    fourModeBtn->setToolTip("4画面");
    fourModeBtn->setCheckable(true);
    fourModeBtn->setChecked(currentDisplayMode == FourMode); // 根据当前模式设置选中状态
    fourModeBtn->setAutoExclusive(true);
    fourModeBtn->setFixedSize(30, 30);
    displayModeGroup->addButton(fourModeBtn, FourMode);

    // 添加四画面文字标签
    fourLabel = new QLabel("四画面");
    fourLabel->setAlignment(Qt::AlignCenter);
    fourLabel->setStyleSheet("QLabel { color: white; font-size: 12px; font-weight: bold; }");

    // 添加按钮和标签到垂直布局
    fourLayout->addWidget(fourModeBtn, 0, Qt::AlignCenter);
    fourLayout->addWidget(fourLabel, 0, Qt::AlignCenter);

    // 添加到主布局
    modeLayout->addWidget(fourWidget);

    // 九画面布局已注释
    QVBoxLayout *nineLayout = new QVBoxLayout();
    nineLayout->setContentsMargins(0, 0, 0, 0);
    nineLayout->setSpacing(2);
    QWidget *nineWidget = new QWidget();
    nineWidget->setLayout(nineLayout);

    // 9画面按钮
    nineModeBtn = new QToolButton();
    nineModeBtn->setIcon(QIcon(":/images/9_def.png"));
    nineModeBtn->setIconSize(QSize(25, 25));
    nineModeBtn->setToolTip("9画面");
    nineModeBtn->setCheckable(true);
    nineModeBtn->setChecked(currentDisplayMode == NineMode); // 根据当前模式设置选中状态
    nineModeBtn->setAutoExclusive(true);
    nineModeBtn->setFixedSize(30, 30);
    displayModeGroup->addButton(nineModeBtn, NineMode);

    // 添加九画面文字标签
    nineLabel = new QLabel("九画面");
    nineLabel->setAlignment(Qt::AlignCenter);
    nineLabel->setStyleSheet("QLabel { color: white; font-size: 12px; font-weight: bold; }");

    // 添加按钮和标签到垂直布局
    nineLayout->addWidget(nineModeBtn, 0, Qt::AlignCenter);
    nineLayout->addWidget(nineLabel, 0, Qt::AlignCenter);

    // 添加到主布局
    modeLayout->addWidget(nineWidget);

    // 添加右侧弹簧使按钮组居中
    modeLayout->addStretch();

    // 连接信号槽已注释
    connect(displayModeGroup, SIGNAL(buttonClicked(int)), this, SLOT(changeDisplayMode(int)));

    // 设置样式已注释
    QString btnStyle = "QToolButton { background: transparent; border: none; }";
    singleModeBtn->setStyleSheet(btnStyle);
    fourModeBtn->setStyleSheet(btnStyle);
    nineModeBtn->setStyleSheet(btnStyle);

    // 设置初始状态图标已注释
    updateDisplayModeIcons();
}

void MainWindow::updateDisplayModeIcons()
{
    // 根据按钮状态更新图标
    singleModeBtn->setIcon(QIcon(singleModeBtn->isChecked() ? ":/images/1.png" : ":/images/1_def.png"));
    fourModeBtn->setIcon(QIcon(fourModeBtn->isChecked() ? ":/images/4.png" : ":/images/4_def.png"));
    nineModeBtn->setIcon(QIcon(nineModeBtn->isChecked() ? ":/images/9.png" : ":/images/9_def.png"));

    // 更新文字标签颜色
    singleLabel->setStyleSheet(QString("QLabel { color: %1; font-size: 12px; font-weight: bold; }")
                                   .arg(singleModeBtn->isChecked() ? "#00FFFF" : "white"));
    fourLabel->setStyleSheet(QString("QLabel { color: %1; font-size: 12px; font-weight: bold; }")
                                 .arg(fourModeBtn->isChecked() ? "#00FFFF" : "white"));
    nineLabel->setStyleSheet(QString("QLabel { color: %1; font-size: 12px; font-weight: bold; }")
                                 .arg(nineModeBtn->isChecked() ? "#00FFFF" : "white"));
}

void MainWindow::changeDisplayMode(int mode)
{
    // 更新当前显示模式
    currentDisplayMode = static_cast<DisplayMode>(mode);

    // 更新按钮图标
    updateDisplayModeIcons();

    // 重新布局视频窗口
    layoutChild();
}

void MainWindow::layoutChild()
{
    // 获取可用空间
    int availableWidth = mainContent->width();
    int availableHeight = mainContent->height();

    // 计算每个视频窗口的大小
    int videoCount = showList.size();
    if (videoCount == 0)
        return;

    // 根据当前显示模式确定布局方式
    int cols;
    int rows;

    switch (currentDisplayMode)
    {
    case SingleMode:
        cols = 1;
        rows = 1;
        break;
    case FourMode:
        cols = 2;
        rows = 2;
        break;
    case NineMode:
        cols = 3;
        rows = 3;
        break;
    default:
        cols = 2;                              // 默认为2列
        rows = (videoCount + cols - 1) / cols; // 向上取整计算行数
    }

    // 设置视频间距
    const int spacing = 10; // 视频之间的间距
    const int margin = 10;  // 边缘间距

    // 计算每个视频窗口的宽高（考虑间距）
    int videoWidth = (availableWidth - (spacing * (cols - 1)) - (margin * 2)) / cols;
    int videoHeight = (availableHeight - (spacing * (rows - 1)) - (margin * 2)) / rows;

    // 如果是单画面模式且只有一个视频，让它占据整个区域但保留边距
    if (currentDisplayMode == SingleMode && videoCount > 0)
    {
        // 只显示第一个视频，隐藏其他视频
        for (int i = 0; i < videoCount; ++i)
        {
            if (i == 0)
            {
                // 在单画面模式下添加边距
                showList[i]->setGeometry(margin, margin,
                                         availableWidth - (margin * 2),
                                         availableHeight - (margin * 2));
                showList[i]->setVisible(true);
            }
            else
            {
                showList[i]->setVisible(false);
            }
        }
        return;
    }

    // 布局多个视频窗口
    for (int i = 0; i < videoCount; ++i)
    {
        // 根据当前模式决定是否显示
        bool shouldShow = true;

        if ((currentDisplayMode == FourMode && i >= 4) ||
            (currentDisplayMode == NineMode && i >= 9))
        {
            shouldShow = false;
        }

        if (shouldShow)
        {
            int row = i / cols;
            int col = i % cols;

            // 计算位置时考虑间距和边距
            int x = margin + col * (videoWidth + spacing);
            int y = margin + row * (videoHeight + spacing);

            // 设置视频窗口的位置和大小
            showList[i]->setGeometry(x, y, videoWidth, videoHeight);
            showList[i]->setVisible(true);
        }
        else
        {
            showList[i]->setVisible(false);
        }
    }
}

void MainWindow::addWindowSlot()
{
    OneVideo *one = new OneVideo(mainContent);
    connect(one, SIGNAL(closeSignal(OneVideo *)), SLOT(childClosed(OneVideo *)));
    one->show();
    showList.append(one);

    layoutChild();
}

void MainWindow::removeWindowSlot()
{
    for (OneVideo *one : showList)
    {
        one->deleteLater();
    }
    showList.clear();
    layoutChild();
}

void MainWindow::aboutSlot()
{
    QMessageBox::aboutQt(this);
}

void MainWindow::showConfigDialog()
{
    ConfigDialog dialog(this);
    dialog.exec();
}

void MainWindow::timerSlot()
{
    // 移除时间显示
    statusBar()->clearMessage();
}

void MainWindow::childClosed(OneVideo *who)
{
    // 从映射中查找并移除
    QString rtspToRemove;
    for (auto it = rtspVideoMap.begin(); it != rtspVideoMap.end(); ++it)
    {
        if (it.value() == who)
        {
            rtspToRemove = it.key();
            break;
        }
    }

    if (!rtspToRemove.isEmpty())
    {
        rtspVideoMap.remove(rtspToRemove);
    }

    showList.removeOne(who);
    layoutChild();
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);

    // 更新标题宽度
    if (ui->titleLabel)
    {
        ui->titleLabel->setFixedWidth(this->width() * 0.6); // 设置标题宽度为窗口宽度的60%
    }

    layoutChild(); // 重新布局视频窗口
}

// 当在扫描界面选择摄像头时，自动创建一个新的视频窗口并连接
void MainWindow::onCameraSelected(const QString &rtspUrl)
{
    // 如果URL不为空，连接到摄像头
    if (!rtspUrl.isEmpty())
    {
        // 检查是否已经有该URL对应的视频窗口
        if (rtspVideoMap.contains(rtspUrl))
        {
            // 该摄像头已经显示，不需要重复创建
            return;
        }

        // 创建新窗口
        OneVideo *targetVideo = new OneVideo(mainContent);
        connect(targetVideo, SIGNAL(closeSignal(OneVideo *)), SLOT(childClosed(OneVideo *)));
        targetVideo->show();
        showList.append(targetVideo);

        // 记录RTSP URL与视频窗口的映射关系
        rtspVideoMap[rtspUrl] = targetVideo;

        // 重新布局
        layoutChild();

        // 获取摄像头信息，找出名称
        QList<CameraInfo> cameras = ConfigManager::instance().loadCamerasFromDatabase();
        for (const CameraInfo &camera : cameras)
        {
            if (camera.rtspUrl == rtspUrl && !camera.name.isEmpty())
            {
                // 设置摄像头名称
                targetVideo->setCameraName(camera.name);
                LOG_INFO("set camera name: " << camera.name);
                break;
            }
        }

        // 设置RTSP URL并开始播放
        targetVideo->setRtspUrl(rtspUrl);

        LOG_INFO("show camera: " << rtspUrl);
    }
}

// 当在扫描界面取消选择摄像头时，关闭对应的视频窗口
void MainWindow::onCameraDeselected(const QString &rtspUrl)
{
    // 如果URL不为空，关闭对应的视频窗口
    if (!rtspUrl.isEmpty() && rtspVideoMap.contains(rtspUrl))
    {
        OneVideo *targetVideo = rtspVideoMap[rtspUrl];

        // 首先停止视频线程
        targetVideo->stopNetwork();

        // 从映射中移除，先移除映射关系，防止回调时再次访问
        rtspVideoMap.remove(rtspUrl);

        // 从列表中移除
        showList.removeOne(targetVideo);

        // 使用延长的定时器确保线程有足够的时间停止
        QTimer *delayTimer = new QTimer(this);
        delayTimer->setSingleShot(true);
        connect(delayTimer, &QTimer::timeout, [targetVideo, delayTimer]()
                {
            // 关闭视频窗口并释放资源
            targetVideo->close();
            targetVideo->deleteLater();
            delayTimer->deleteLater(); });
        delayTimer->start(1000); // 增加到1秒

        // 重新布局
        layoutChild();

        LOG_INFO("close camera: " << rtspUrl);
    }
}

// 处理摄像头名称更新
void MainWindow::onCameraNameUpdated(const QString &rtspUrl, const QString &newName)
{
    // 如果URL不为空且存在对应的视频窗口，更新摄像头名称
    if (!rtspUrl.isEmpty() && rtspVideoMap.contains(rtspUrl))
    {
        OneVideo *targetVideo = rtspVideoMap[rtspUrl];

        // 更新摄像头名称
        targetVideo->setCameraName(newName);

        LOG_INFO("update camera name: " << rtspUrl << " -> " << newName);
    }
}

void MainWindow::toggleMaximized()
{
    if (isMaximized())
    {
        // 还原窗口
        showNormal();
    }
    else
    {
        // 保存当前窗口位置和大小
        normalGeometry = geometry();
        // 最大化窗口
        showMaximized();
    }
}

bool MainWindow::eventFilter(QObject *watched, QEvent *event)
{
    // 处理标题栏的鼠标事件
    if (watched == ui->bannerWidget)
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            if (mouseEvent->button() == Qt::LeftButton)
            {
                dragPosition = mouseEvent->globalPos() - frameGeometry().topLeft();
                mousePressed = true;
                return true;
            }
        }
        else if (event->type() == QEvent::MouseMove)
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            if (mouseEvent->buttons() & Qt::LeftButton && mousePressed)
            {
                move(mouseEvent->globalPos() - dragPosition);
                return true;
            }
        }
        else if (event->type() == QEvent::MouseButtonRelease)
        {
            mousePressed = false;
        }
        else if (event->type() == QEvent::MouseButtonDblClick)
        {
            // 双击标题栏切换最大化/还原
            toggleMaximized();
            return true;
        }
    }
    return QMainWindow::eventFilter(watched, event);
}

void MainWindow::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        dragPosition = event->globalPos() - frameGeometry().topLeft();
        mousePressed = true;
    }
    QMainWindow::mousePressEvent(event);
}

void MainWindow::mouseMoveEvent(QMouseEvent *event)
{
    if (event->buttons() & Qt::LeftButton && mousePressed)
    {
        // 只有在标题栏区域拖动时才移动窗口
        if (event->pos().y() < 40)
        {
            move(event->globalPos() - dragPosition);
        }
    }
    QMainWindow::mouseMoveEvent(event);
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event)
{
    mousePressed = false;
    QMainWindow::mouseReleaseEvent(event);
}

// 右侧操作按钮槽函数实现
void MainWindow::onEavesdropClicked()
{
    // 窃视功能实现
    QMessageBox::information(this, "窃视功能", "窃视功能已激活");
    LOG_INFO("Eavesdrop function activated");
}

void MainWindow::onListenClicked()
{
    // 窃听功能实现
    QMessageBox::information(this, "窃听功能", "窃听功能已激活");
    LOG_INFO("Listen function activated");
}

void MainWindow::onScreenshotClicked()
{
    // 截屏：对第一个可见的视频窗口执行
    for (OneVideo *one : showList)
    {
        if (one->isVisible())
        {
            one->takeScreenshot();
            LOG_INFO("Screenshot triggered via right panel");
            return;
        }
    }
    QMessageBox::information(this, "截屏", "当前没有可用的视频窗口");
}

void MainWindow::onRecordClicked()
{
    // 录像：对第一个可见的视频窗口执行
    for (OneVideo *one : showList)
    {
        if (one->isVisible())
        {
            one->toggleRecording();
            LOG_INFO("Recording toggled via right panel");
            return;
        }
    }
    QMessageBox::information(this, "录像", "当前没有可用的视频窗口");
}
